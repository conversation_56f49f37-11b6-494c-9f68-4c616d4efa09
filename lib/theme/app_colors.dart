import 'package:flutter/material.dart';

/// 应用颜色系统
/// 
/// 这个类定义了应用中使用的所有颜色常量，替代了对 okshop_common 包的依赖。
/// 所有颜色值都与原始的 OKColor 定义保持一致，确保UI效果不变。
class AppColors {
  // 私有构造函数，防止实例化
  AppColors._();

  // 主要颜色
  /// 主色 - 橙色
  static const Color primary = Color(0xFFF89328);
  
  /// 主色加重版 - 深橙色
  static const Color primaryWeight = Color(0xFFE66F53);
  
  /// 次要色 - 黄橙色
  static const Color secondary = Color(0xFFF7AB00);
  
  /// 强调色 - 深橙红色
  static const Color accent = Color(0xFFED4C00);

  // 功能颜色
  /// 错误色 - 红色
  static const Color error = Color(0xFFE02020);
  
  /// 破坏性操作色 - 与错误色相同
  static const Color destructive = error;
  
  /// 必填项色 - 深红色
  static const Color must = Color(0xffe00707);
  
  /// VIP色 - 橙红色
  static const Color vip = Color(0xfffa5700);
  
  /// 零售模式色 - 浅橙色
  static const Color retail = Color(0xFFE0A471);
  
  /// 线条色 - 绿色
  static const Color line = Color(0xFF00c330);
  
  /// 标签栏色 - 深灰蓝色
  static const Color tab = Color(0xff3e4b5a);
  
  /// 阴影色 - 半透明黑色
  static const Color shadow = Color(0x29000000);

  // 灰色系列
  /// 深黑灰色
  static const Color gray22 = Color(0xff222222);
  
  /// 深灰色
  static const Color gray33 = Color(0xff333333);
  
  /// 中深灰色
  static const Color gray4D = Color(0xff4d4d4d);
  
  /// 中灰色
  static const Color gray58 = Color(0xff585858);
  
  /// 标准灰色
  static const Color gray66 = Color(0xff666666);
  
  /// 中浅灰色
  static const Color gray70 = Color(0xFF707070);
  
  /// 浅灰色
  static const Color grayB9 = Color(0xffb9b9b9);
  
  /// 更浅灰色
  static const Color grayBF = Color(0xffbfbfbf);
  
  /// 很浅灰色
  static const Color grayDD = Color(0xffdddddd);
  
  /// 极浅灰色
  static const Color grayF7 = Color(0xFFF7F7F7);

  // 兼容性别名 - 为了保持与原有代码的兼容性
  /// 主色别名
  static const Color Primary = primary;
  
  /// 主色加重版别名
  static const Color PrimaryWeight = primaryWeight;
  
  /// 次要色别名
  static const Color Secondary = secondary;
  
  /// 强调色别名
  static const Color Accent = accent;
  
  /// 错误色别名
  static const Color Error = error;
  
  /// 破坏性操作色别名
  static const Color Destructive = destructive;
  
  /// 必填项色别名
  static const Color Must = must;
  
  /// VIP色别名
  static const Color Vip = vip;
  
  /// 零售模式色别名
  static const Color Retail = retail;
  
  /// 线条色别名
  static const Color Line = line;
  
  /// 标签栏色别名
  static const Color Tab = tab;
  
  /// 阴影色别名
  static const Color Shadow = shadow;
  
  /// 深黑灰色别名
  static const Color Gray22 = gray22;
  
  /// 深灰色别名
  static const Color Gray33 = gray33;
  
  /// 中深灰色别名
  static const Color Gray4D = gray4D;
  
  /// 中灰色别名
  static const Color Gray58 = gray58;
  
  /// 标准灰色别名
  static const Color Gray66 = gray66;
  
  /// 中浅灰色别名
  static const Color Gray70 = gray70;
  
  /// 浅灰色别名
  static const Color GrayB9 = grayB9;
  
  /// 更浅灰色别名
  static const Color GrayBF = grayBF;
  
  /// 很浅灰色别名
  static const Color GrayDD = grayDD;
  
  /// 极浅灰色别名
  static const Color GrayF7 = grayF7;
}
