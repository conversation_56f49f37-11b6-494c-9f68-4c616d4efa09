import 'dart:convert';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart' as h;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:muyipork/app/models/brands_info.dart' hide City;
import 'package:muyipork/app/models/brands_invoice.dart';
import 'package:muyipork/app/models/city.dart';
import 'package:muyipork/app/models/district.dart';
import 'package:muyipork/app/models/jwt.dart';
import 'package:muyipork/app/models/local_settings.dart';
import 'package:muyipork/app/models/pay_method.dart';
import 'package:muyipork/app/models/setting_pay.dart';
import 'package:muyipork/app/models/setting_point.dart';
import 'package:muyipork/app/models/shipping_delivery.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import 'package:package_info/package_info.dart';

class PrefProvider {
  static const _host_development = 'dev-api-diner-app.omos.tw';
  static const _host_production = 'api-diner-app.omos.tw';
  static const _invoiceApiKeyDev = 'S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY';
  static const _invoiceApiKeyProd = 'S6FR5FQE-S6FR-S6FR-S6FR-S6FR5FQE2SMA';
  static const _invoiceApiUrlDev = 'https://webtest.bpscm.com.tw/SCMWEBAPI/API';
  static const _invoiceApiUrlProd = 'https://www.bpscm.com.tw/SCMWebAPI/api';
  static const _soapUrlDev =
      'https://webtest.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL';
  static const _soapUrlProd =
      'https://www.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL';

  final PackageInfo packageInfo;
  final BoxProvider boxProvider;
  h.Box get userDefault => boxProvider.userDefault;

  // PrefProvider() : box = Hive.box('settings');
  PrefProvider({
    @required this.packageInfo,
    @required this.boxProvider,
  });

  // 支付方式
  set payMethods(Iterable<PayMethod> value) {
    final jsonString = jsonEncode(value.toList());
    userDefault.put(Keys.PayMethods.value, jsonString);
  }

  Iterable<PayMethod> get payMethods {
    final jsonString =
        userDefault.get(Keys.PayMethods.value, defaultValue: '[]');
    return List.from(jsonDecode(jsonString)).map((e) => PayMethod.fromJson(e));
  }

  set cities(Iterable<City> value) {
    final jsonString = jsonEncode(value.toList());
    userDefault.put('cities', jsonString);
  }

  Iterable<City> get cities {
    final jsonString = userDefault.get('cities', defaultValue: '[]');
    return List.from(jsonDecode(jsonString)).map((e) => City.fromJson(e));
  }

  set districts(Iterable<District> value) {
    final jsonString = jsonEncode(value.toList());
    userDefault.put('districts', jsonString);
  }

  Iterable<District> get districts {
    final jsonString = userDefault.get('districts', defaultValue: '[]');
    return List.from(jsonDecode(jsonString)).map((e) => District.fromJson(e));
  }

  Iterable<District> getDistricts(num cityId) {
    return districts.where((element) => element.cityId == cityId);
  }

  /// FIXME:
  bool get hasPrinter {
    return true;
    // final index = box.get(kKeyInnerPrinter, defaultValue: 0);
    // return SunmiPrinterStatus.FoundSunmiPrinter.index == index;
    // final model = box.get(kKeyModel, defaultValue: '');
    // return kKeyV2Pro == model;
  }

  // fcm token
  String get fcmToken => userDefault.get(kFcmToken, defaultValue: '');
  set fcmToken(String value) => userDefault.put(kFcmToken, value);

  bool get isSandbox => isDevelopment;

  bool get isDevelopment {
    final packageName = this.packageInfo.packageName ?? '';
    return GetUtils.hasMatch(packageName, r'\.dev');
  }

  // TODO: remove this, use settings instead.
  SettingGetRes get setting {
    final jsonString = userDefault.get(Keys.Setting.value, defaultValue: '{}');
    final jsonObject = jsonDecode(jsonString);
    return SettingGetRes.fromJson(jsonObject);
  }

  set setting(SettingGetRes value) {
    final jsonObject = value.toJson();
    final jsonString = jsonEncode(jsonObject);
    final src = userDefault.get(Keys.Setting.value, defaultValue: '{}');
    if (src != jsonString) {
      userDefault.put(Keys.Setting.value, jsonString);
    }
  }

  Stream<SettingGetRes> get settingStream {
    return userDefault.watch(key: Keys.Setting.value).map((event) {
      final jsonObject = jsonDecode(event.value);
      return SettingGetRes.fromJson(jsonObject);
    });
  }

  String get posBAN => '83193989';
  String get soapUrl => isDevelopment ? _soapUrlDev : _soapUrlProd;
  String get invoiceUrl =>
      isDevelopment ? _invoiceApiUrlDev : _invoiceApiUrlProd;
  String get invoiceApiKey =>
      isDevelopment ? _invoiceApiKeyDev : _invoiceApiKeyProd;
  String get host => isDevelopment ? _host_development : _host_production;

  ValueListenable<h.Box> getListenalbe({Iterable<dynamic> keys}) {
    return userDefault.listenable(keys: keys);
  }

  String get token => userDefault.get(kKeyToken, defaultValue: '');

  set token(String value) {
    if (token != value) {
      logger.d('Token - set token($value)');
      // 先設定 token 到 boxProvider 產生 domain
      boxProvider.token = token;
      userDefault.put(kKeyToken, value ?? '');
    }
  }

  Jwt get jwt {
    // final a = JwtDecoder.getExpirationDate(this.token);
    // final b = JwtDecoder.getRemainingTime(this.token);
    // final c = JwtDecoder.getTokenTime(this.token);
    // final d = JwtDecoder.isExpired(this.token);
    // final json = JwtDecoder.decode(this.token);
    final json = JwtDecoder.tryDecode(this.token) ?? <String, dynamic>{};
    return Jwt.fromJson(json);
  }

  bool get isLogin => token.isNotEmpty && !jwt.isExpired;
  bool get isLogout => !isLogin;

  BrandsInfo get brandsInfo {
    final jsonString =
        userDefault.get(Keys.BrandsInfo.value, defaultValue: '{}') as String;
    return BrandsInfo.fromRawJson(jsonString);
  }

  set brandsInfo(BrandsInfo value) {
    final dest = value.toRawJson();
    final src =
        userDefault.get(Keys.BrandsInfo.value, defaultValue: '{}') as String;
    if (src != dest) {
      userDefault.put(Keys.BrandsInfo.value, dest);
    }
  }

  Stream<BrandsInfo> get brandsInfoStream {
    return userDefault.watch(key: Keys.BrandsInfo.value).map((event) {
      return BrandsInfo.fromRawJson(event.value);
    });
  }

  //讀取餐飲模式時段顯示變數
  OrdersBusinessHoursMode get ordersBusinessHoursMode {
    final index = userDefault.get(kKeyOrdersBusinessHoursMode, defaultValue: 0);
    return OrdersBusinessHoursMode.values.elementAt(index);
  }

  set ordersBusinessHoursMode(OrdersBusinessHoursMode value) {
    userDefault.put(kKeyOrdersBusinessHoursMode, value.index);
  }

  void toggleOrdersBusinessHoursMode() {
    if (OrdersBusinessHoursMode.Current == ordersBusinessHoursMode) {
      //切換至 Other
      ordersBusinessHoursMode = OrdersBusinessHoursMode.Other;
    } else {
      //切換至 Current
      ordersBusinessHoursMode = OrdersBusinessHoursMode.Current;
    }
  }

  // 時段串流
  Stream<OrdersBusinessHoursMode> ordersBusinessHoursModeStream() {
    return userDefault
        .watch(key: kKeyOrdersBusinessHoursMode)
        .map((event) => OrdersBusinessHoursMode.values.elementAt(event.value));
  }

  // 讀取經營模式變數 (OrderView title 會用到)
  StoreType get storeType {
    final index =
        userDefault.get(kKeyStoreType, defaultValue: StoreType.Dinner.index);
    final storeType = StoreType.values.elementAt(index);
    // 確認是否有這個型態
    if (brandsType.contains(storeType)) {
      // 包含，正常回傳
      return storeType;
    }
    // 異常，零售卻儲存餐飲
    if (storeType.isDinner) {
      // 故回傳零售
      return StoreType.Retail;
    }
    // 異常，餐飲卻儲存零售
    if (storeType.isRetail) {
      // 故回傳餐飲
      return StoreType.Dinner;
    }
    return StoreType.Max;
  }

  // 儲存經營模式變數
  set storeType(StoreType value) {
    // 檢查是否有這個型態
    if (brandsType.contains(value)) {
      userDefault.put(kKeyStoreType, value.index);
    }
  }

  void toggleStoreType() {
    if (StoreType.Dinner == storeType) {
      storeType = StoreType.Retail;
    } else {
      storeType = StoreType.Dinner;
    }
  }

  Stream<StoreType> storeTypeStream() {
    return userDefault.watch(key: kKeyStoreType).map((event) {
      return StoreType.values[event.value];
    });
  }

  // 品牌業態
  BrandsType get brandsType => setting.checkoutType;

  Stream<BrandsType> brandsTypeStream() {
    return settingStream.map((event) => event.checkoutType).distinct();
  }

  //特殊，避免 Pref 內的 StoreType 停在錯誤的模式底下。
  //在開發者選項設定過 checkoutType 後要做這個動作
  void verifySettings(SettingGetRes settingGetRes) {
    BrandsType settingBrandsType = settingGetRes.data.other.getBrandsType();
    if (settingBrandsType == BrandsType.BeforeDinner ||
        settingBrandsType == BrandsType.AfterDinner) {
      //Prevent from StoreType is Retail
      if (storeType == StoreType.Retail) {
        storeType = StoreType.Dinner;
      }
    } else if (settingBrandsType == BrandsType.Retail) {
      //Prevent from StoreType is Meal
      if (storeType == StoreType.Dinner) {
        storeType = StoreType.Retail;
      }
    }
  }

  //直接取得當前主色 (只有雙態的版本)
  Color get themeColor {
    if (storeType.isRetail) {
      return AppColors.Retail;
    }
    if (storeType.isDinner) {
      switch (ordersBusinessHoursMode) {
        case OrdersBusinessHoursMode.Other:
        // 下時段顏色只在訂單頁面呈現
        // return AppColors.Secondary;
        case OrdersBusinessHoursMode.Current:
          return AppColors.Primary;
        default:
          return AppColors.Primary;
      }
    }
    return AppColors.Primary;
  }

  BrandsInvoice get brandsInvoice {
    // HACK:
    // return BrandsInvoice(
    //   taxId: '83193989',
    //   taxType: 1,
    //   guiException: 1,
    //   status: 1,
    // );
    final jsonString = userDefault.get(kKeyBrandsInvoice, defaultValue: '{}');
    return BrandsInvoice.fromRawJson(jsonString);
  }

  set brandsInvoice(BrandsInvoice value) {
    userDefault.put(kKeyBrandsInvoice, value.toRawJson());
  }

  bool get invoiceEnabled {
    // 發票總開關
    if (brandsInvoice.invoiceEnabled) {
      // 發票單次開關
      if (brandsInvoice.invoiceSwitchEnabled) {
        // 單次開關啟用，判斷本地
        return localSettings.nnPrintInvoice.switcher.isOn;
      } else {
        // 一定開發票 (禁用單次開關)
        return true;
      }
    }
    return false;
  }

  Iterable<SettingPay> get settingPay {
    final jsonString = userDefault.get(Keys.SettingPay, defaultValue: '[]');
    final jsonObject = jsonDecode(jsonString);
    return List.from(jsonObject).map((e) => SettingPay.fromJson(e));
  }

  set settingPay(final Iterable<SettingPay> value) {
    final dest = jsonEncode(value?.toList() ?? []);
    final src = userDefault.get(Keys.SettingPay, defaultValue: '[]');
    if (dest != src) {
      userDefault.put(Keys.SettingPay, dest);
    }
  }

  Iterable<SettingPoint> get settingPoint {
    final jsonString = userDefault.get(Keys.SettingPoint, defaultValue: '[]');
    final jsonObject = jsonDecode(jsonString);
    return List.from(jsonObject).map((e) => SettingPoint.fromJson(e));
  }

  set settingPoint(final Iterable<SettingPoint> value) {
    final dest = jsonEncode(value?.toList() ?? []);
    final src = userDefault.get(Keys.SettingPoint, defaultValue: '[]');
    if (dest != src) {
      userDefault.put(Keys.SettingPoint, dest);
    }
  }

  Stream<Iterable<SettingPay>> settingPayStream() {
    return userDefault.watch(key: Keys.SettingPay).map((event) {
      final jsonObject = jsonDecode(event.value);
      return List.from(jsonObject).map((e) => SettingPay.fromJson(e));
    });
  }

  LocalSettings get localSettings {
    final jsonString = userDefault.get(Keys.LocalSettings, defaultValue: '{}');
    final ret = LocalSettings.fromRawJson(jsonString);
    ret.printItemReceipt ??= Switcher.On.index;
    ret.printItemReceiptCount ??= 1;
    ret.printReceipt ??= Switcher.On.index;
    ret.printReceiptCount ??= 1;
    return ret;
  }

  set localSettings(LocalSettings value) {
    final jsonString = value.toRawJson();
    userDefault.put(Keys.LocalSettings, jsonString);
  }

  Stream<LocalSettings> localSettingsStream() {
    return userDefault.watch(key: Keys.LocalSettings).map((event) {
      return LocalSettings.fromRawJson(event.value);
    });
  }

  ///
  /// 取得運費設定
  ///
  ShippingDelivery get shippingDelivery {
    final jsonString =
        userDefault.get('setting/shipping/delivery', defaultValue: '{}');
    return ShippingDelivery.fromRawJson(jsonString);
  }

  ///
  /// 設定運費
  ///
  set shippingDelivery(ShippingDelivery value) {
    final jsonString = value.toRawJson();
    userDefault.put('setting/shipping/delivery', jsonString);
  }

  ///
  /// 取得設定
  ///
  SettingGetData get settingData {
    setting.data ??= SettingGetData();
    return setting.data;
  }
}
