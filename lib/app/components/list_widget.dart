import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/theme/app_colors.dart';

import 'dialog_general.dart';
import 'message_page.dart';

class ListWidget {
  static Widget divider() => const Divider(height: 1.0);

  static Widget _blank({
    String buttonText,
    VoidCallback onPressed,
  }) {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/app_icon.svg',
        width: 142.0,
        fit: BoxFit.contain,
      );
      if (buttonText != null && buttonText.isNotEmpty) {
        yield OutlinedButton(
          onPressed: onPressed,
          child: Text(
            buttonText ?? '',
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        );
      }
    }

    return children().column();
  }

  static Widget blank({
    String buttonText,
    VoidCallback onPressed,
  }) {
    return Background(
      background: ListView(),
      child: Center(
        child: _blank(
          buttonText: buttonText,
          onPressed: onPressed,
        ),
      ),
    );
  }

  static Widget message(
    String message, {
    String buttonText,
    VoidCallback onPressed,
  }) {
    return Background(
      background: ListView(),
      child: MessagePage(
        icon: DialogContentIcon.Alert,
        message: message ?? '',
        buttonText: buttonText,
        onPressed: onPressed,
      ),
    );
  }

  static Widget bottomProgressing() {
    return const Center(
      child: const Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 20.0,
        ),
        child: const CircularProgressIndicator(),
      ),
    );
  }

  static Widget header(String text) {
    return SizedBox(
      height: Constants.buttonHeight,
      child: Center(
        child: Text(
          text ?? '',
          style: const TextStyle(
            color: AppColors.Primary,
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
