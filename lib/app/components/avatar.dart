import 'package:flutter/material.dart';
import 'package:muyipork/app/models/store_account.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/extension.dart';

class Avatar extends StatelessWidget {
  final StoreAccount data;

  const Avatar({
    Key key,
    @required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 60.0,
          height: 60.0,
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: const [
              const BoxShadow(
                color: const Color(0x4dc68329),
                offset: const Offset(3, 3),
                blurRadius: 12,
              ),
            ],
          ),
          child: Icon(
            Icons.person,
            color: AppColors.Primary,
            size: 48.0,
          ),
        ),
        const SizedBox(
          width: 12.0,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                this.data?.role?.name ?? '',
                style: const TextStyle(
                  fontSize: 15,
                  color: const Color(0xff936230),
                ),
                textAlign: TextAlign.left,
              ),
              Row(
                children: [
                  Text(
                    this.data?.name ?? '',
                    style: const TextStyle(
                      fontSize: 22,
                      color: const Color(0xff000000),
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(
                    width: 8.0,
                  ),
                  Expanded(
                    child: Text(
                      this.data?.comment ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        color: const Color(0xff5c4c4c),
                      ),
                      softWrap: false,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(
          width: 12.0,
        ),
        Container(
          alignment: Alignment.topCenter,
          padding: const EdgeInsets.symmetric(
            horizontal: 6.0,
            vertical: 2.0,
          ),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(const Radius.circular(5.0)),
            color: data.status.switcher.backgroundColor,
          ),
          child: Text(
            // this.data.status.switcher.name  ?.displayStatus ?? '',
            data?.status?.switcher?.name ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
