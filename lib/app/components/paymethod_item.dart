import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:muyipork/theme/app_colors.dart';

class PaymethodItem extends StatelessWidget {
  final String title;
  final String image;
  final bool showTitle;

  const PaymethodItem({
    Key key,
    this.title,
    this.image,
    this.showTitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 2,
      ),
      width: 154, // TODO: do NOT limit
      height: 68, // TODO: do NOT limit
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.white,
        border: Border.all(
          width: 1.0,
          color: const Color(0xffffdbb5),
        ),
      ),
      child: Center(child: _item()),
    );
  }

  Widget _item() {
    if (showTitle ?? false) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _icon(),
          Visibility(
            visible: title != null && title.isNotEmpty,
            child: SizedBox(
              width: 12,
            ),
          ),
          Text(
            title ?? '',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.Gray58,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }
    return Image.asset(
      image,
    );
  }

  Widget _icon() {
    return DecoratedBox(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.Gray70,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 12,
        ),
        child: SizedBox.fromSize(
          size: Size.square(42),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: SvgPicture.asset(
              'assets/images/icon_payment.svg',
              color: Colors.white,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }
}
