import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:muyipork/theme/app_colors.dart';

class SearchBar extends StatelessWidget {
  final ValueChanged<String> onValueChanged;
  final String hintText;

  const SearchBar({
    Key key,
    this.onValueChanged,
    this.hintText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: DecoratedBox(
        decoration: ShapeDecoration(
          shape: StadiumBorder(
            side: BorderSide(
              width: 1.0,
              color: AppColors.GrayDD,
            ),
          ),
          color: Colors.white,
          shadows: const [
            const BoxShadow(
              color: const Color(0x29000000),
              offset: const Offset(0.0, 0.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: TextFormField(
          onChanged: onValueChanged,
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
          ],
          decoration: InputDecoration(
            prefixIcon: const Icon(
              Icons.search,
            ),
            border: InputBorder.none,
            hintText: hintText ?? '',
            hintStyle: const TextStyle(
              fontSize: 16,
              color: AppColors.Gray66,
            ),
          ),
        ),
      ),
    );
  }
}
