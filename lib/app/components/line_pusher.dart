import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';

import 'line_alert.dart';

class LinePusher extends StatelessWidget {
  final ValueChanged<String> onSendPressed;
  final message = ''.obs;

  LinePusher({
    this.onSendPressed,
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          width: 300.0,
          child: const LineAlert(),
        ),
        const SizedBox(
          height: 22.0,
        ),
        ColoredBox(
          color: AppColors.Line,
          child: Row(
            children: [
              const SizedBox(
                width: 12.0,
              ),
              Expanded(
                child: TextField(
                  onChanged: this.message,
                  autofocus: true,
                  inputFormatters: [
                    // FilteringTextInputFormatter.singleLineFormatter,
                  ],
                  decoration: InputDecoration(
                    border: const OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: kBorderRadius,
                    ),
                    fillColor: Colors.white,
                    filled: true,
                    hintText: 'LINE留言給顧客…',
                    hintStyle: const TextStyle(
                      fontSize: 16,
                      color: const Color(0xff666666),
                    ),
                    contentPadding: kContentPadding,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                ),
                onPressed: () {
                  if (this.onSendPressed != null) {
                    this.onSendPressed?.call(this.message.value);
                  } else {
                    Get.back(
                      result: this.message.value,
                    );
                  }
                },
              ),
            ],
          ).paddingSymmetric(
            vertical: 8.0,
          ),
        ),
      ],
    );
  }
}
