import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:muyipork/theme/app_colors.dart';

class BgDefault extends StatelessWidget {
  final Widget child;
  final Color mainColor;

  const BgDefault({
    Key key,
    this.child,
    this.mainColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Background(
      background: ColoredBox(
        color: AppColors.Primary,
        child: SvgPicture.asset(
          'assets/images/bg_default.svg',
          fit: BoxFit.fitWidth,
          width: double.infinity,
          alignment: Alignment.topCenter,
        ),
      ),
      child: SizedBox.expand(
        child: child ?? const SizedBox(),
      ),
    );
  }
}
