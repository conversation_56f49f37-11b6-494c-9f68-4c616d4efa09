import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/theme/app_colors.dart';

class Badge extends StatelessWidget {
  final String text;
  final Color textColor;
  final Color backgroundColor;
  final Widget leading;
  final num size;
  final num fontSize;

  const Badge({
    Key key,
    this.text,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.leading,
    this.size,
    this.fontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _size = size?.toDouble() ?? 24;
    return Container(
      height: _size,
      padding: EdgeInsets.symmetric(horizontal: 6),
      constraints: BoxConstraints(
        minWidth: _size,
      ),
      alignment: Alignment.center,
      decoration: ShapeDecoration(
        color: backgroundColor,
        shape: StadiumBorder(),
      ),
      child: _main(),
    );
  }

  Widget _main() {
    final ls = <Widget>[];
    ls.addIf(leading != null, leading);
    ls.addIf(
      leading != null && text != null && text.isNotEmpty,
      SizedBox(width: 4),
    );
    ls.addIf(
      text != null && text.isNotEmpty,
      Text(
        text,
        style: TextStyle(
          fontSize: fontSize?.toDouble() ?? 16,
          color: textColor ?? AppColors.Primary,
        ),
        textAlign: TextAlign.center,
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }
}
