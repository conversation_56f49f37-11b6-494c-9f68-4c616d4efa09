import 'package:flutter/material.dart';
import 'package:muyipork/theme/app_colors.dart';

class ImageItem extends StatelessWidget {
  static const _ICON_SIZE = 90.0;
  final Function onPressed;
  final Function onDeletePressed;
  final Widget child;

  const ImageItem({
    Key key,
    this.onPressed,
    this.onDeletePressed,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox.fromSize(
      size: Size.square(_ICON_SIZE),
      child: Stack(
        children: [
          GestureDetector(
            onTap: onPressed,
            child: Container(
              width: _ICON_SIZE,
              height: _ICON_SIZE,
              decoration: const BoxDecoration(
                borderRadius:
                    const BorderRadius.all(const Radius.circular(2.0)),
                color: AppColors.GrayDD,
                // image: DecorationImage(
                //   image: CachedNetworkImageProvider(
                //     this.imageUrl,
                //   ),
                // ),
              ),
              child: child ?? SizedBox.shrink(),
            ),
          ),
          Visibility(
            visible: onDeletePressed != null,
            child: OverflowBox(
              alignment: Alignment.topRight,
              child: Transform.translate(
                offset: Offset(20, -20),
                child: _remove(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _remove() {
    return IconButton(
      icon: Container(
        padding: EdgeInsets.all(4),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: const [
            const BoxShadow(
              color: const Color(0x29000000),
              offset: const Offset(0.0, 3.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Icon(
          Icons.delete,
          color: AppColors.GrayBF,
        ),
      ),
      onPressed: onDeletePressed,
    );
  }
}
