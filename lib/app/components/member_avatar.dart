import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:muyipork/theme/app_colors.dart';

class MemberAvatar extends StatelessWidget {
  static const _SIZE = 36.0;
  final num size;
  final String imageUrl;

  const MemberAvatar({
    Key key,
    this.size,
    this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: imageUrl != null && imageUrl.isNotEmpty,
      child: _body(),
      replacement: _placeholder(),
    );
  }

  Widget _body() {
    final _size = size ?? _SIZE;
    return CachedNetworkImage(
      width: _size,
      height: _size,
      imageUrl: imageUrl ?? '',
      imageBuilder: (context, imageProvider) {
        return DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: imageProvider,
            ),
          ),
        );
      },
      placeholder: (context, url) => _placeholder(),
    );
  }

  Widget _placeholder() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Color(0xffeeeef3),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person,
        color: AppColors.Primary,
        size: size ?? _SIZE,
      ),
    );
  }
}
