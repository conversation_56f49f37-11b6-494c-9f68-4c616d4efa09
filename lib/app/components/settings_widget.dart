import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:url_launcher/url_launcher.dart';

import 'custom_editor.dart';
import 'underline_divider.dart';

class SettingsWidget {
  static Widget link({
    final String titleText,
    final String subtitleText,
    final Color tileColor,
  }) {
    final hasSomething = subtitleText != null && subtitleText.isNotEmpty;
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: tileColor ?? Colors.white,
      title: CustomEditor(
        onTap: () {
          final urlString = subtitleText ?? '';
          canLaunch(urlString).then((value) {
            if (value) {
              launch(urlString);
            }
          });
        },
        readonly: true,
        initialValue: hasSomething ? subtitleText : ' ',
        labelText: titleText,
        valueColor: kColorPrimary,
        maxLines: 3,
      ),
      trailing: TextButton.icon(
        onPressed: () {
          Clipboard.setData(ClipboardData(
            text: subtitleText ?? '',
          ));
          DialogGeneral(DialogArgs(
            mainButtonText: '確定',
            contentIcon: DialogContentIcon.Okay,
            content: Center(
              child: Text(
                '${titleText ?? ''}已複製',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ),
          )).dialog();
        },
        icon: const Icon(
          Icons.link,
        ),
        label: Text(
          '複製網址',
          style: const TextStyle(
            fontSize: 14,
            color: const Color(0xff3e4b5a),
          ),
          textAlign: TextAlign.left,
        ),
        style: TextButton.styleFrom(
          primary: const Color(0x803E4B5A),
          padding: kChipPadding,
          backgroundColor: const Color(0xffeeeef3),
          shape: const StadiumBorder(
            side: const BorderSide(
              width: 1.0,
              color: const Color(0xffdbdbea),
            ),
          ),
        ),
      ),
    );
  }

  static Widget comment({
    final String titleText,
    final String hintText,
    final String initialValue,
    final ValueChanged<String> onChanged,
    final String Function(String) validator,
    final List<TextInputFormatter> inputFormatters,
    final EdgeInsetsGeometry contentPadding,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: contentPadding ?? kContentPadding,
          child: Text(
            // '店家備註',
            titleText ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.Gray22,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        DecoratedBox(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(2.0)),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Color(0x33000000),
                offset: Offset(0.0, 1.0),
                blurRadius: 0.0,
              ),
            ],
          ),
          child: Padding(
            padding: contentPadding ?? kContentPadding,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: TextFormField(
                initialValue: initialValue ?? '',
                onChanged: onChanged,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
                decoration: InputDecoration.collapsed(
                  // hintText: '請輸入200字內文字',
                  hintText: hintText ?? '',
                  hintStyle: const TextStyle(
                    fontSize: 16,
                    color: AppColors.GrayBF,
                  ),
                  // contentPadding: kContentPadding,
                ),
                maxLines: 5,
                validator: validator,
                inputFormatters: inputFormatters,
              ),
            ),
          ),
        ),
      ],
    );
  }

  static Widget input({
    final ValueChanged<String> onChanged,
    final String Function(String) validator,
    final String labelText,
    final String hintText,
    final String initialValue,
    final TextInputType keyboardType,
    final List<TextInputFormatter> inputFormatters,
    final bool readonly,
    final Function onTap,
    final Widget suffixIcon,
    final Widget suffix,
    final TextEditingController controller,
    final bool isCollapsed,
    final EdgeInsetsGeometry contentPadding,
    final Color backgroundColor,
  }) {
    return ListTile(
      contentPadding: contentPadding ?? kContentPadding,
      tileColor: backgroundColor ?? Colors.white,
      title: CustomEditor(
        controller: controller,
        onTap: onTap,
        readonly: readonly ?? false,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
        initialValue: initialValue,
        labelText: labelText,
        hintText: hintText,
        onChanged: onChanged,
        validator: validator,
        suffixIcon: suffixIcon,
        suffix: suffix,
        isCollapsed: isCollapsed,
      ),
    );
  }

  static Widget divider({
    final double indent,
    final double endIndent,
    final Color backgroundColor,
    final Color color,
  }) {
    return ColoredBox(
      color: backgroundColor ?? Colors.white,
      child: Divider(
        color: color,
        height: 1.0,
        indent: indent ?? kPadding,
        endIndent: endIndent ?? kPadding,
      ),
    );
  }

  static Widget switcher({
    final String titleText,
    final bool value,
    final ValueChanged<bool> onChanged,
    final EdgeInsetsGeometry contentPadding,
    final Color tileColor,
  }) {
    return UnderlineDivider(
      backgroundColor: tileColor,
      insets: contentPadding,
      child: SwitchListTile(
        dense: true,
        contentPadding: contentPadding ?? kContentPadding,
        value: value ?? false,
        onChanged: onChanged,
        title: Text(
          // '線上內用接單功能',
          titleText ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }

  static Widget title({
    final String titleText,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10.0,
        horizontal: kPadding,
      ),
      child: Text(
        // '菜單設定',
        titleText ?? '',
        style: const TextStyle(
          fontSize: 14.0,
          color: AppColors.Gray66,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  static Widget space() {
    return const ColoredBox(
      color: Colors.white,
      child: SizedBox(
        height: 4.0,
        width: double.infinity,
      ),
    );
  }

  static Widget item({
    Key key,
    final String titleText,
    final Function onPressed,
    final EdgeInsetsGeometry contentPadding,
    final Color tileColor,
    final Widget leading,
    final Widget title,
    final Widget trailing,
  }) {
    return UnderlineDivider(
      insets: contentPadding,
      backgroundColor: tileColor,
      child: ListTile(
        leading: leading,
        onTap: onPressed,
        dense: true,
        contentPadding: contentPadding ?? kContentPadding,
        // tileColor: tileColor ?? Colors.white,
        key: key,
        title: title ??
            Text(
              // '線上外送接單功能',
              titleText ?? '',
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              maxLines: 1,
            ),
        trailing: trailing ??
            const Icon(
              Icons.navigate_next,
            ),
      ),
    );
  }
}
