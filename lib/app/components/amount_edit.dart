import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:muyipork/theme/app_colors.dart';

class AmountEdit extends StatefulWidget {
  AmountEdit({
    this.editingValue,
    this.onChanged,
    Key key,
  }) : super(key: key) {
    final value = (editingValue ?? 0).round();
    textEditingController.text = '$value';
  }

  final num editingValue;
  final ValueChanged<num> onChanged;
  final TextEditingController textEditingController =
      FixedOffsetTextEditingController();

  @override
  _AmountEditState createState() => _AmountEditState();
}

class _AmountEditState extends State<AmountEdit> {
  // TextEditingController textEditingController;
  TextEditingController get textEditingController =>
      widget.textEditingController;

  @override
  void initState() {
    super.initState();
    // final value = widget.editingValue.round();
    // textEditingController = TextEditingController(text: '$value');
  }

  // @override
  // void didUpdateWidget(covariant AmountEdit oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   final value = widget.editingValue.round();
  //   textEditingController.text = '$value';
  // }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: _main(),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(_minus()); // -
    children.add(SizedBox(width: 4));
    children.add(_input()); // 0
    children.add(SizedBox(width: 4));
    children.add(_plus()); // +
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _plus() {
    return _Button(
      iconData: Icons.add,
      onPressed: () {
        final currentValue = num.tryParse(textEditingController.text) ?? 0;
        if (currentValue != null) {
          final nextValue = (currentValue + 1).round();
          textEditingController.text = '$nextValue';
          widget.onChanged?.call(nextValue);
        }
      },
    );
  }

  Widget _input() {
    return SizedBox(
      width: 100,
      child: TextField(
        style: TextStyle(fontSize: 30.0),
        controller: textEditingController,
        decoration: InputDecoration(
          fillColor: Colors.white,
          filled: true,
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey, width: 0.5),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey, width: 0.5),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey, width: 0.5),
          ),
          disabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey, width: 0.5),
          ),
          contentPadding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
          isDense: true,
        ),
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        onChanged: (value) {
          widget.onChanged?.call(int.tryParse(textEditingController.text));
        },
        onTap: () {
          // 鍵盤彈出時，選中全部
          Future.delayed(Duration(milliseconds: 100), () {
            textEditingController.selection = TextSelection(
              baseOffset: 0,
              extentOffset: textEditingController.text.length,
            );
          });
        },
      ),
    );
  }

  Widget _minus() {
    return _Button(
      iconData: Icons.remove,
      onPressed: () {
        final currentValue = num.tryParse(textEditingController.text) ?? 0;
        if (currentValue != null && currentValue > 0) {
          final nextValue = (currentValue - 1).round();
          textEditingController.text = '$nextValue';
          widget.onChanged?.call(nextValue);
        }
      },
    );
  }
}

class _Button extends StatelessWidget {
  final IconData iconData;
  final Function onPressed;

  const _Button({
    key,
    @required this.iconData,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.GrayDD,
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: IconButton(
        padding: EdgeInsets.zero,
        icon: Icon(
          iconData,
          color: AppColors.Gray70,
        ),
        onPressed: onPressed,
      ),
    );
  }
}

// ref: https://stackoverflow.com/a/66038182
class FixedOffsetTextEditingController extends TextEditingController {
  @override
  set text(String newText) {
    value = value.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
      composing: TextRange.empty,
    );
  }
}
