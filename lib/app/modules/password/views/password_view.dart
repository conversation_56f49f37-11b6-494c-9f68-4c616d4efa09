import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/theme/app_colors.dart';

import '../controllers/password_controller.dart';

class PasswordView extends GetView<PasswordController> {
  const PasswordView({
    Key key,
  }) : super(key: key);

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(
        future: controller.submit(),
      ).dialog();
      if (true == ret) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '密碼修改',
      child: BottomWidgetPage.save(
        onPressed: _submit,
        child: _main(),
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(SizedBox(height: 12));
    children.add(Text(
      '請填寫密碼修改資料', // TODO: i18n
      style: const TextStyle(
        fontSize: 16,
        color: AppColors.Primary,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ));
    children.add(SizedBox(height: 12));
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          obscureText: true,
          labelText: '舊密碼', // TODO: i18n
          hintText: '請輸入目前密碼', // TODO: i18n
          onChanged: (value) => controller.draft.oldPassword = value,
          validator: (value) {
            final data = controller.draft.oldPassword;
            if (data == null || data.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          obscureText: true,
          labelText: '新密碼', // TODO: i18n
          hintText: '請輸入新密碼', // TODO: i18n
          onChanged: (value) => controller.draft.newPassword = value,
          validator: (value) {
            final data = controller.draft.newPassword;
            if (data == null || data.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          obscureText: true,
          labelText: '再次輸入新密碼', // TODO: i18n
          hintText: '請輸入相同的密碼', // TODO: i18n
          onChanged: (value) => controller.draft.checkPassword = value,
          validator: (value) {
            final x = controller.draft.newPassword;
            final y = controller.draft.checkPassword;
            if (x != y) {
              return '請輸入相同的新密碼';
            }
            return null;
          },
        ),
      ),
    );
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      physics: AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }
}
