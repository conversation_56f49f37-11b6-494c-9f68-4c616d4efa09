import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_pay_add_controller.dart';

class SettingPayAddView extends GetView<SettingPayAddController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      // titleText: '新增/編輯支付方式',
      titleText: controller.titleText,
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            child: _main(),
            onPressed: _submit,
          );
        },
        onError: ListWidget.message,
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: 12));
    children.addIf(
      true,
      Text(
        // '請輸入名稱及上傳照片',
        '請輸入名稱',
        style: TextStyle(
          fontSize: 16,
          color: AppColors.Primary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(
      true,
      _element().paddingSymmetric(horizontal: kPadding),
    );
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      physics: AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  Widget _element() {
    final children = <Widget>[];
    children.addIf(true, _icon());
    children.addIf(true, SizedBox(width: 12));
    children.addIf(true, Expanded(child: _input()));
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 26),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.white,
        border: Border.all(
          width: 1.0,
          color: const Color(0xffffdbb5),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: children,
      ),
    );
  }

  CustomEditor _input() {
    return CustomEditor(
      initialValue: controller.selectedDraft.name,
      onChanged: (value) {
        controller.selectedDraft.name = value;
      },
      decoration: InputDecoration(
        hintText: '自訂名稱',
        border: InputBorder.none,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
    );
  }

  Widget _icon() {
    return DecoratedBox(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Color(0xFF707070),
      ),
      child: SizedBox.fromSize(
        size: Size.square(42),
        child: SvgPicture.asset(
          'assets/images/icon_payment.svg',
        ).paddingSymmetric(
          vertical: 12,
        ),
      ).paddingSymmetric(
        vertical: 12,
      ),
    );
  }

  void _submit() {
    FutureProgress<bool>(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }
}
