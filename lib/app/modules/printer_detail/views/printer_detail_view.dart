import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/amount_edit.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/checkbox_item.dart';
import 'package:muyipork/app/components/dialog_actions.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:esc_pos_utils/esc_pos_utils.dart';

import '../controllers/printer_detail_controller.dart';

class PrinterDetailView extends GetView<PrinterDetailController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions(),
      titleText: controller.containsCloud ? '雲印表機詳情' : '印表機詳情',
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            onPressed: _submit,
            child: Obx(() => _main()),
            // bottom: _bottom(),
          );
        },
        onError: ListWidget.message,
      ),
    );
  }

  List<Widget> _actions() {
    final children = <Widget>[];
    children.addIf(
      // controller.draft.id != null && controller.draft.id > 0,
      false,
      IconButton(
        icon: Icon(
          Icons.remove_circle_outline,
          color: Colors.white,
        ),
        onPressed: _onRemovePressed,
      ),
    );
    children.addIf(
      true,
      Obx(() {
        if (controller.draft?.id is num && controller.draft.id > 0) {
          return TextButton(
            onPressed: _onRemovePressed,
            child: Text(
              '刪除',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          );
        }
        return SizedBox();
      }),
    );
    return children;
  }

  void _onRemovePressed() {
    final completer = Completer<bool>();
    DialogGeneral.quest(
      '即將刪除印表機',
      mainButtonText: '刪除',
      secondaryButtonText: '取消',
      onMainButtonPressed: () {
        completer.complete(true);
      },
      onSecondaryButtonPress: () {
        completer.complete(false);
      },
    ).dialog().then(
      (value) async {
        if (true == await completer.future) {
          _delete();
        }
      },
    );
  }

  void _delete() {
    FutureProgress(
      future: controller.delete(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back(result: DataAction.Delete);
        }
      },
    );
  }

  void _submit() {
    final update = controller.draft.id is num && controller.draft.id > 0;
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back(result: update ? DataAction.Update : DataAction.Create);
        }
      },
    );
  }

  void _printPreview() {
    FutureProgress(
      future: controller.draft.printTestPage(
          PaperSize.init(controller.draft.printerPaperSize.index)),
    ).dialog();
  }

  List<Widget> _children() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.input(
        labelText: '名稱',
        hintText: '請輸入名稱',
        initialValue: controller.draft.name,
        onChanged: (value) {
          controller.draft.name = value;
        },
      ),
    );
    children.addIf(
      controller.containsCloud,
      SettingsWidget.input(
        labelText: '雲印表機序號',
        hintText: '請輸入序號',
        initialValue: controller.draft.serialNo,
        onChanged: (value) {
          controller.draft.serialNo = value;
        },
      ),
    );
    children.addIf(
      controller.normalPrinter,
      Container(
        padding: EdgeInsets.symmetric(horizontal: kPadding, vertical: 8),
        color: Colors.white,
        child: TextButton(
          style: ButtonStyle(
            minimumSize: MaterialStateProperty.all(Size(double.infinity, 30.0)),
            padding: MaterialStateProperty.all(EdgeInsets.zero),
            shape: MaterialStateProperty.all(StadiumBorder(
              side: BorderSide(color: const Color(0xffdbdbea)),
            )),
            textStyle: MaterialStateProperty.all(Get.textTheme.subtitle1),
            backgroundColor: MaterialStateProperty.all(const Color(0xffeeeef3)),
          ),
          onPressed: _printPreview,
          child: Text(
            '列印測試頁',
            style: TextStyle(
              fontSize: 14,
              color: const Color(0xff3e4b5a),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    children.addIf(controller.normalPrinter, SizedBox(height: 12));
    children.addIf(controller.normalPrinter, _paperSize());
    children.addIf(controller.normalPrinter, SettingsWidget.space());
    children.addIf(true, SizedBox(height: 12));
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '開啟列印功能',
        value: controller.draft.status.switcher.isOn,
        onChanged: (value) {
          controller.draft.status = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(true, SettingsWidget.space());
    children.addIf(
      controller.containsCloud,
      Text(
        '*注意：開啟列印功能後將同時啟動”LINE自動接單”。',
        style: TextStyle(
          fontSize: 14,
          color: AppColors.Error,
        ),
        textAlign: TextAlign.left,
      ).paddingOnly(left: kPadding),
    );
    children.addIf(true, SizedBox(height: 2));
    children.addIf(
      controller.containsCloud,
      SettingsWidget.item(
        onPressed: _showPrintCountPicker,
        titleText: '列印張數',
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              // '1張',
              controller.displayPrintCount ?? '',
              style: TextStyle(
                fontSize: 15,
                color: const Color(0xff333333),
              ),
              textAlign: TextAlign.right,
            ),
            Icon(
              Icons.navigate_next,
            ),
          ],
        ),
      ),
    );
    children.addIf(controller.containsCloud, SettingsWidget.space());
    children.addIf(
      controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '發票',
        checkboxValue:
            controller.draft.containsCategory(PrintType.invoice.value),
        onCheckboxChanged: (value) {
          controller.draft.setHasCategory(PrintType.invoice.value, value);
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '消費明細',
        checkboxValue:
            controller.draft.containsCategory(PrintType.receiptLite.value),
        onCheckboxChanged: (value) {
          controller.draft.setHasCategory(PrintType.receiptLite.value, value);
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '商品明細',
        checkboxValue:
            controller.draft.containsCategory(PrintType.receiptItem.value),
        onCheckboxChanged: (value) {
          controller.draft.setHasCategory(PrintType.receiptItem.value, value);
          controller.refreshDraft();
        },
      ),
    );
    // FIXME:
    children.addIf(
      false &&
          controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '日結單',
        checkboxValue:
            controller.draft.containsCategory(PrintType.reportStatements.value),
        onCheckboxChanged: (value) {
          controller.draft
              .setHasCategory(PrintType.reportStatements.value, value);
          controller.refreshDraft();
        },
      ),
    );
    // FIXME:
    children.addIf(
      false &&
          controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '商品統計',
        checkboxValue:
            controller.draft.containsCategory(PrintType.reportSales.value),
        onCheckboxChanged: (value) {
          controller.draft.setHasCategory(PrintType.reportSales.value, value);
          controller.refreshDraft();
        },
      ),
    );
    // FIXME:
    children.addIf(
      controller.draft.status.switcher.isOn && controller.normalPrinter,
      CheckboxItem(
        checkboxTitle: '工作單',
        checkboxValue:
            controller.draft.containsCategory(PrintType.sticker.value),
        onCheckboxChanged: (value) {
          controller.draft.setHasCategory(PrintType.sticker.value, value);
          controller.refreshDraft();
        },
      ),
    );
    children.addAllIf(
      controller.draft.status.switcher.isOn &&
          controller.normalPrinter &&
          controller.draft.containsCategory(PrintType.sticker.value),
      _categories(),
    );
    return children;
  }

  void _showPrintCountPicker() async {
    final actions = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
    DialogActions(
      titleText: '選擇列印張數',
      actions: actions,
    ).dialog().then((index) {
      if (index >= 0 && index < actions.length) {
        final printCount = num.parse(actions.elementAt(index)) ?? 1;
        controller.draft.printCount = printCount;
        controller.refreshDraft();
      }
    });
  }

  Widget _paperSize() {
    final children = <Widget>[];
    children.addIf(
      true,
      Expanded(
        child: Text(
          '紙張尺寸',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    children.addIf(
      true,
      Expanded(
        child: RadioButton<PrinterPaperSize>(
          titleText: '58mm',
          value: PrinterPaperSize.mm58,
          groupValue: controller.draft.printerPaperSize,
          onChanged: (value) {
            controller.draft.printerPaperSize = value;
            controller.refreshDraft();
          },
        ),
      ),
    );
    children.addIf(
      true,
      Expanded(
        child: RadioButton<PrinterPaperSize>(
          titleText: '80mm',
          value: PrinterPaperSize.mm80,
          groupValue: controller.draft.printerPaperSize,
          onChanged: (value) {
            controller.draft.printerPaperSize = value;
            controller.refreshDraft();
          },
        ),
      ),
    );
    return UnderlineDivider(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ).paddingSymmetric(
        horizontal: kPadding,
      ),
    );
  }

  Iterable<Widget> _categories() {
    final list = <Widget>[];
    list.addAllIf(
      controller.prefProvider.brandsType.containsDinner,
      _dinnerApp(),
    );
    list.addAllIf(
      controller.prefProvider.brandsType.containsDinner,
      _dinnerLine(),
    );
    list.addAllIf(
      controller.prefProvider.brandsType.containsRetail,
      _dinnerRetail(),
    );
    return list;
  }

  Iterable<Widget> _dinnerApp() {
    final list = <Widget>[];
    list.addIf(true, _header('餐飲店內分類'));
    list.addAllIf(true, controller.dinnerApp.map((e) => _cate(e)));
    return list;
  }

  Iterable<Widget> _dinnerLine() {
    final list = <Widget>[];
    list.addIf(true, _header('餐飲線上分類'));
    list.addAllIf(true, controller.dinnerLine.map((e) => _cate(e)));
    return list;
  }

  Iterable<Widget> _dinnerRetail() {
    final list = <Widget>[];
    list.addIf(true, _header('零售分類'));
    list.addAllIf(true, controller.retail.map((e) => _cate(e)));
    return list;
  }

  static Widget _header(String text) {
    return Text(text ?? '').paddingOnly(
      left: 34,
      top: 8,
      bottom: 8,
    );
  }

  Widget _cate(Category element) {
    final children = <Widget>[];
    children.add(SizedBox(width: kPadding));
    children.add(
      Expanded(
        child: CheckboxItem(
          checkboxTitle: element.name,
          checkboxValue: controller.draft.containsCategory(element.id),
          onCheckboxChanged: (b) {
            controller.draft.setHasCategory(element.id, b);
            controller.refreshDraft();
          },
        ),
      ),
    );
    controller.draft.categorySettings['${element.id}'] ??= CategorySetting();
    final categorySettings = controller.draft.categorySettings['${element.id}'];
    children.addIf(
      controller.draft.containsCategory(element.id),
      AmountEdit(
        editingValue: categorySettings.printCount ?? 1,
        onChanged: (value) {
          categorySettings.printCount = max(1, value);
        },
      ),
    );
    children.add(SizedBox(width: kPadding));
    return ColoredBox(
      color: Colors.white,
      child: Row(children: children),
    );
  }

  Widget _main() {
    return ListView(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      physics: AlwaysScrollableScrollPhysics(),
      children: _children(),
    );
  }
}
