import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/invoice_widget.dart';
import 'package:muyipork/app/components/product_info_item.dart';
import 'package:muyipork/app/components/rounded_button.dart';
import 'package:muyipork/app/components/rounded_outlined_button.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/models/qr_format.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/modules/orders_confirm/controllers/orders_confirm_controller.dart';
import 'package:muyipork/app/modules/orders_setup/controllers/orders_setup_controller.dart';
import 'package:muyipork/app/modules/tables_selection/controllers/tables_selection_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import 'package:screenshot/screenshot.dart';
import 'package:tuple/tuple.dart';

import '../controllers/orders_adjust_controller.dart';

class OrdersAdjustView extends GetView<OrdersAdjustController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: WillPopScope(
        onWillPop: () async {
          controller.draft.ensureNoZeroQuantityItem();
          Get.back(result: controller.hasDataChanged);
          return false;
        },
        child: StandardPage(
          backgroundColor: controller.prefProvider.themeColor,
          titleText: '訂單小計',
          onBackPress: _onBackPress,
          actions: _actions(),
          child: controller.obx(
            (state) {
              return BottomWidgetPage(
                child: _main(),
                bottom: _bottomSheet(),
              );
            },
          ),
        ),
      ),
    );
  }

  List<Widget> _actions() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Obx(
        () {
          //Only display the table select button if kind is 0
          if (controller.draft != null && controller.args.kind == 0) {
            return Visibility(
              visible: controller.draft.shouldSelectTable(),
              child: Container(
                padding: EdgeInsets.all(8),
                child: RoundedOutlinedButton(
                    text: controller.selectedTableDisplayName(),
                    onPressed: () async {
                      //Open the tables selection page.
                      // TODO: 置換參數，使用結構，移除 Tuple3
                      Tuple3<int, int, String> resultPartitionTable =
                          await Get.toNamed(Routes.TABLES_SELECTION,
                              arguments: TablesSelectionArgs(
                                table1Id: controller.draft.table1Id,
                                table2Id: controller.draft.table2Id,
                              )) as Tuple3<int, int, String>;

                      if (resultPartitionTable != null) {
                        // print('Got resultPartitionTable [' + resultPartitionTable.item1.toString() + '][' + resultPartitionTable.item2.toString() + ']');
                        //設定回傳值給 ordersPostReq.
                        controller.draft.table1Id = resultPartitionTable.item1;
                        controller.draft.table2Id = resultPartitionTable.item2;
                        controller.refreshDraft();
                        //Data changed.
                        controller.hasDataChanged = true;
                      }
                    }),
              ),
            );
          } else {
            return Container();
          }
        },
      ),
    );
    ls.addIf(false, _vip());
    return ls;
  }

  Widget _vip() {
    return IconButton(
      iconSize: 36.0,
      icon: SizedBox.fromSize(
        // size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_crown.svg',
          color: Colors.white,
        ),
      ),
      onPressed: controller.vip.toggle,
    );
  }

  Future<Button> _interruptionConfirm() {
    final completer = Completer<Button>();
    DialogGeneral.quest(
      '0元不產生發票',
      onMainButtonPressed: () => completer.complete(Button.Positive),
      onSecondaryButtonPress: () => completer.complete(Button.Negative),
      mainButtonText: '繼續結帳',
    ).dialog();
    return completer.future;
  }

  Future<void> _checkout() async {
    final draft = controller.draft;
    draft.invoice = controller.prefProvider.invoiceEnabled;
    // 發票
    if (draft.invoice == true) {
      // 零元確認
      if (draft.total is num && draft.total <= 0) {
        final selected = await _interruptionConfirm();
        if (Button.Negative == selected) {
          // 取消結帳...
          return;
        }
        // continue without invoice
        draft.invoice = false;
      }
    }
    final value =
        await FutureProgress(future: controller.checkout()).dialog<num>();
    if (value is num && value > 0) {
      // 成功，回到首頁
      Get.until((route) => Get.currentRoute == Routes.HOME);
    }
  }

  Widget _total() {
    final price = controller.draft.getTotal(
      coupon: controller.coupon,
      // NOTE: 服務費不在此計算，尚未決定如何呈現
      // TODO: 加入服務費
      // settingOrderFee:
      //     controller.prefProvider.settingData.settingOrderFee,
    );
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 24,
          color: AppColors.Gray22,
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: '總計:',
          ),
          TextSpan(
            text: controller.draft.normalItemsCount.decimalStyle,
            style: TextStyle(
              color: AppColors.Primary,
            ),
          ),
          TextSpan(
            text: '項  金額:',
          ),
          TextSpan(
            text: '${(price ?? 0).decimalStyle}',
            style: TextStyle(
              color: AppColors.Primary,
            ),
          ),
          TextSpan(
            text: '元',
          ),
        ],
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }

  Widget _bottomSheet() {
    Iterable<Widget> children() sync* {
      yield Padding(
        padding: const EdgeInsets.only(
          top: 32,
          bottom: 0,
          // right: kPadding,
        ),
        child: Obx(() => _total()),
      );
      yield RoundedButton(
        // padding: const EdgeInsets.all(8.0),
        child: Obx(() {
          final checkout = controller.mode.value == OrdersAdjustMode.CheckOut;
          return checkout ? _checkoutButtons() : _editButtons();
        }),
      );
      yield const SizedBox(height: 12.0);
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 20.0,
      ),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, -0.65),
          colors: [
            Color(0x00ffffff),
            Colors.white,
          ],
          stops: [0.0, 1.0],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _checkoutButtons() {
    const availableBrandTypes = [
      BrandsType.BeforeDinner,
      BrandsType.Retail,
    ];
    final showCheckout =
        availableBrandTypes.contains(controller.prefProvider.brandsType);
    return YesNoButton.withArgs(
      left: YesNoButtonArgs(
        buttonText: '繼續選購',
        onPressed: () {
          //Ensure there's no 0 quantity data!
          controller.draft.ensureNoZeroQuantityItem();
          Get.back();
        },
      ),
      mid: showCheckout
          ? YesNoButtonArgs(
              buttonText: '現金快結',
              backgroundColor: AppColors.Error,
              onPressed: _checkout,
            )
          : null,
      right: YesNoButtonArgs(
        buttonText: '下一步',
        onPressed: () async {
          //Ensure there's no 0 quantity data!
          controller.draft.ensureNoZeroQuantityItem();
          await Get.toNamed(
            Routes.ORDERS_CONFIRM,
            arguments: OrdersConfirmArgs(
              ordersPostReq: controller.draft,
              kind: controller.args.kind,
            ),
          );
          controller.refreshDraft();
        },
      ),
    );
  }

  Widget _editButtons() {
    return YesNoButton.withArgs(
      left: YesNoButtonArgs(
        buttonText: '繼續選購',
        onPressed: () async {
          // 打開編輯頁面.
          final ordersPostReq = await Get.toNamed(
            Routes.ORDERS_SETUP,
            arguments: OrdersSetupArgs(
              kind: controller.args.kind,
              mode: OrdersSetupMode.EditExisting,
              existOrdersPostReq: controller.draft,
            ),
          );
          if (ordersPostReq is OrdersPostReq) {
            controller.refreshDraft();
            // 強制設定為編輯過
            controller.hasDataChanged = true;
          }
        },
      ),
      right: YesNoButtonArgs(
        buttonText: '離開編輯',
        onPressed: () {
          //Ensure there's no 0 quantity data!
          controller.draft.ensureNoZeroQuantityItem();
          Get.back(result: controller.hasDataChanged);
        },
      ),
    );
  }

  void _onBackPress() {
    //這個阻擋編輯總價不可為0的邏輯根本會造成流程卡住
    // if (controller.totalItemPrice() <= 0) {
    //   DialogGeneral.show(
    //       DialogArgs(
    //         header: DialogGeneral.titleText('提醒'),
    //         contentIcon: DialogContentIcon.Alert,
    //         content: DialogGeneral.centerContentText('商品總價不可為0！'),
    //         mainButtonText: '確認',
    //       ),
    //       barrierDismissible: true);
    //   return;
    // }
    controller.draft.ensureNoZeroQuantityItem();
    Get.back(result: controller.hasDataChanged);
  }

  Widget _memberCouponDiscount(num value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      color: kColorBackground,
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 17,
            color: AppColors.Gray22,
          ),
          children: [
            TextSpan(
              text: '優惠券折抵：',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              // text: '-\$20',
              text: (0 - (value ?? 0)).currencyStyle,
              style: TextStyle(
                color: controller.prefProvider.themeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              text: '元',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _memberCouponExtraPrice(num value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      color: kColorBackground,
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 17,
            color: AppColors.Gray22,
          ),
          children: [
            TextSpan(
              text: '優惠券金額：',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              // text: '\$0',
              text: (value ?? 0).currencyStyle,
              style: TextStyle(
                color: controller.prefProvider.themeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              text: '元',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    // 發票截圖
    children.addIf(
      true,
      Obx(() {
        if (controller.invoice == null) {
          return SizedBox.shrink();
        }
        final ret = Screenshot(
          controller: controller.invoiceScreenshotController,
          child: InvoiceWidget(data: controller.invoice),
        );
        controller.widgetUpdater.complete();
        return ret;
      }),
    );
    // 國防布
    children.addIf(
      true,
      ColoredBox(
        color: kColorBackground,
        child: SizedBox.expand(),
      ),
    );
    // 使用者實際看到的預覽介面
    children.addIf(
      true,
      Obx(() => _slave()),
    );
    return Stack(
      alignment: Alignment.topCenter,
      children: children,
    );
  }

  Widget _slave() {
    final children = <Widget>[];
    children.addIf(true, _coupon());
    children.addIf(true, _message());
    children.addIf(
      (controller.draft.memberCouponDiscount ?? 0) != 0,
      _memberCouponDiscount(controller.draft.memberCouponDiscount),
    );
    children.addIf(
      (controller.draft.memberCouponExtraPrice ?? 0) != 0,
      _memberCouponExtraPrice(controller.draft.memberCouponExtraPrice),
    );
    children.addIf(
      true,
      Expanded(
        child: Obx(() => _list()),
      ),
    );
    return Column(children: children);
  }

  Widget _message() {
    return Obx(() {
      if (controller.couponMessage == null ||
          controller.couponMessage.isEmpty) {
        return SizedBox.shrink();
      }
      return Center(
        child: Text(
          controller.couponMessage ?? '',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.Error,
          ),
          textAlign: TextAlign.right,
        ),
      );
    });
  }

  ///
  /// 顯示優惠券詳細訊息
  ///
  void _showCouponDetail() {
    Get.toNamed(
      Routes.COUPON_DETAIL,
      parameters: <String, String>{
        Keys.Data: QrFormat(
          memberId: controller.draft.memberId,
          memberCouponId: controller.draft.memberCouponId,
        ).toRawJson(),
        'actions': '0',
      },
    );
  }

  Widget _coupon() {
    return Obx(() {
      if (controller.coupon == null) {
        return SizedBox.shrink();
      }
      return Stack(
        alignment: Alignment.topRight,
        children: [
          Container(
            alignment: Alignment.bottomCenter,
            margin: EdgeInsets.all(kPadding),
            child: CouponItem(
              data: controller.coupon,
              onPressed: _showCouponDetail,
            ),
          ),
          IconButton(
            iconSize: 32,
            icon: DecoratedBox(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFE0E0E0),
                    spreadRadius: 0,
                    blurRadius: 8,
                  ),
                ],
              ),
              child: Icon(
                Icons.cancel,
                color: Colors.white,
              ),
            ),
            onPressed: controller.resetCoupon,
          ),
        ],
      );
    });
  }

  Widget _list() {
    logger.d('[OrdersAdjustView] vip(${controller.vip.value})');
    final list = controller.draft
        .getItems()
        .where((element) => (element.quantity ?? 0) > 0);
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: 140,
      ),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final element = list.elementAt(index);
        final product =
            controller.productProvider.getProduct(element.productId);
        return ProductInfoItem(
          vip: controller.vip.value,
          vipPrice: product?.vipPrice ?? 0,
          vipOnly: product?.isVip?.switcher ?? Switcher.Off,
          title: element.productName,
          summary: element.productSpec1,
          showSummary: true,
          // price: data.singlePrice(),
          price: product?.price ?? 0,
          stackPrice: element.stackPrice,
          themeColor: controller.prefProvider.themeColor,
          //[Dep]: Nope
          // onTap: () async {
          //   //Open ORDER_EDITING with ExitExist mode.
          //   OrderItem editedOrderItem = await Get.toNamed(Routes.ORDER_EDITING, arguments: OrderItem.clone(orderItems[i])) as OrderItem;
          //   if (editedOrderItem != null) {
          //     orderItems[i].paste(editedOrderItem);
          //     controller.ordersPostReq.refresh();
          //     // print('Got editedOrderItem! ');
          //     // controller.addNewOrderItem(addNewOrderItem);
          //   }
          // },
          editingCount: element.quantity,
          onEditingCountChanged: (value) {
            if (value != null) {
              if (controller.productProvider
                      .getProduct(element.productId).stock >
                  0) {
                //Todo: 需要檢查庫存擋掉修改數字?
              }

              // print('set quantity: ' + value.toString());
              element.quantity = value;
              //這邊已經不在需要重算了，因為 finalPrice 其實無關 quantity 改變
              // orderItems[i].finalizePrice(controller.getProductPrice(orderItems[i].productId));
              controller.refreshDraft();
              HapticFeedback.lightImpact();
              controller.hasDataChanged = true;
            }
          },
          // onDeleteTap: () {
          //   HapticFeedback.mediumImpact();
          //   controller.tryRemoveOrderItem(i);
          // }
        );
      },
    );
  }
}
