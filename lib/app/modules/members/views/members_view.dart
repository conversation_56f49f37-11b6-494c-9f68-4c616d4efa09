import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/member_item.dart';
import 'package:muyipork/app/components/qrcode_scanner.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/search_bar.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/qr_format.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import '../controllers/members_controller.dart';

class MembersView extends GetView<MembersController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MembersController>(
      init: MembersController(
        orderProvider: Get.find(),
      ),
      builder: (controller) {
        return StandardPage(
          actions: _actions().toList(growable: false),
          leading: SizedBox.shrink(),
          titleText: '我的會員',
          child: _main(),
        );
      },
    );
  }

  Iterable<Widget> _actions() sync* {
    yield GestureDetector(
      onLongPress: controller.devtool.toggle,
      child: _scan(),
    );
    yield _devtool();
  }

  Widget _devtool() {
    return Obx(() {
      return Visibility(
        visible: controller.devtool.value,
        child: IconButton(
          icon: Icon(
            Icons.build,
            color: Colors.white,
          ),
          onPressed: _showCouponDetail,
        ),
      );
    });
  }

  Future<void> _showCouponDetail() async {
    await Get.toNamed(
      Routes.COUPON_DETAIL,
      parameters: <String, String>{
        Keys.Data: QrFormat(
          memberId: 78,
          memberCouponId: 323,
        ).toRawJson(),
      },
    );
  }

  Widget _scan() {
    return IconButton(
      // iconSize: 36.0,
      icon: SizedBox.fromSize(
        // size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_scan.svg',
          color: Colors.white,
        ),
      ),
      onPressed: _showBarcodePicker,
    );
  }

  void _showBarcodePicker() {
    // HACK: test bar code
    // 不正確字串
    // _parse('/KK2X9NQ');
    // _parse('{type: member, member_id: 1}');
    // 訂單
    // _parse('{"type":"order","order_id":3016}');
    // 不存在會員
    // _parse('{"type":"member","member_id":99999}');
    // 存在一般會員
    // _parse('{"type":"member","member_id":11}');
    // 存在VIP會員
    // _parse('{"type":"member","member_id":12}');
    QrCodeScanner().dialog<String>().then(
      (value) {
        if (value != null && value.isNotEmpty) {
          _parse(value);
        }
      },
    );
  }

  Future<void> _parse(String value) async {
    try {
      final qr = QrFormat.fromRawJson(value);
      if (qr.isMember) {
        // 會員
        await Get.toNamed(
          Routes.MEMBER_DETAIL,
          parameters: <String, String>{
            Keys.Tag: '${qr.memberId}',
            Keys.Data: qr.toRawJson(),
          },
        );
      } else if (qr.isOrder) {
        final id = qr.orderId;
        await Get.toNamed(
          Routes.ORDER_DETAIL,
          parameters: {
            Keys.Tag: '$id',
            Keys.Id: '$id',
          },
        ).then(
          (value) {
            switch (value) {
              case OrderDetailViewShortCut.AcceptOrder:
                _acceptOrder(id);
                break;
              case OrderDetailViewShortCut.RejectOrder:
                _rejectOrder(id);
                break;
              case OrderDetailViewShortCut.CheckoutOrder:
                _checkOutOrder(id);
                break;
              default:
            }
          },
        );
      } else if (qr.isCoupon || qr.isCoupon1 || qr.isCoupon2) {
        await Get.toNamed(
          Routes.COUPON_DETAIL,
          parameters: <String, String>{
            Keys.Data: qr.toRawJson(),
          },
          // arguments: CouponDetailArguments(
          //   checkoutPressed: (value) {
          //     //
          //   },
          //   continuePressed: (value) {
          //     //
          //   },
          // ),
        ).then(
          (value) {
            //
          },
        );
      } else {
        DialogGeneral.alert('不是正確的 QR 碼').dialog();
      }
    } catch (e) {
      // 顯示錯誤訊息
      logger.e(e);
      DialogGeneral.alert('不是正確的 QR 碼').dialog();
    }
  }

  void _acceptOrder(num id) {
    _changeStatus(id, OrderStatus.Accepted);
  }

  void _rejectOrder(num id) {
    // 訂單狀態從 處理中(0) 換成 訂單取消(店家) (3)
    // this.onRejectClicked(data);
    _showRejectDialog().then(
      (value) {
        switch (value) {
          case OrderStatus.CancelByApp:
          case OrderStatus.CancelByLine:
            logger.d('[MemberDetailView] ${value.name}');
            _changeStatus(id, value);
            // _changeStatus(id, OrderStatus.Padding);
            break;
          default:
        }
      },
    );
  }

  void _changeStatus(num id, OrderStatus value) {
    FutureProgress(
      future: controller.orderProvider.putOrderStatus(
        id,
        OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
        OrdersOrderIdStatusPutReq(status: value.index),
      ),
    ).dialog();
  }

  Future<OrderStatus> _showRejectDialog() {
    final completer = new Completer<OrderStatus>();
    final selected = Rx<OrderStatus>(OrderStatus.Padding);
    DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('訂單狀態'),
        mainButtonText: '確認',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          completer.complete(selected.value);
        },
        content: Obx(() {
          final ls = <Widget>[];
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.Padding.name),
              titleText: OrderStatus.Padding.name,
              value: OrderStatus.Padding,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByApp.name),
              titleText: OrderStatus.CancelByApp.name,
              value: OrderStatus.CancelByApp,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByLine.name),
              titleText: OrderStatus.CancelByLine.name,
              value: OrderStatus.CancelByLine,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: ls,
            ),
          );
        }),
      ),
    ).dialog();
    return completer.future;
  }

  // TODO: 最後要移到 orders view 內處理
  void _checkOutOrder(num id) {
    controller.orderProvider.getOrderDetail(id).then(
      (value) async {
        final orderSummary = value.data.asOrderSummary();
        await Get.toNamed(
          Routes.ORDERS_SUM_UP,
          arguments: [orderSummary],
          parameters: <String, String>{
            Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
          },
        );
      },
      onError: (error) {
        //
      },
    );
  }

  Widget _list() {
    logger.d('[MembersView] _list');
    final it = controller.data;
    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      padding: EdgeInsets.only(
        bottom: kBottomPadding,
      ),
      itemCount: it.length + (controller.filter.nnHasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < it.length) {
          final element = it.elementAt(index);
          return MemberItem(
            data: element,
            onPressed: () {
              Get.toNamed(
                Routes.MEMBER_DETAIL,
                parameters: <String, String>{
                  Keys.Tag: '${element.id}',
                  Keys.Data: QrFormat(memberId: element.id).toRawJson(),
                },
              );
            },
          );
        }
        return ListWidget.bottomProgressing();
      },
      separatorBuilder: (context, index) => ListWidget.divider(),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      SearchBar(
        hintText: '請輸入會員暱稱或手機末3碼',
        onValueChanged: (value) {
          controller.filter.keyword = value;
          controller.refreshFilter();
        },
      ),
    );
    children.addIf(
      true,
      Obx(() {
        return _Banner(
          memberCount: controller.memberProvider.total,
          vipCount: controller.memberProvider.vipCount,
        );
      }),
    );
    children.addIf(true, SizedBox(height: 8));
    children.addIf(
      true,
      Expanded(
        child: RefreshIndicator(
          onRefresh: controller.onRefresh,
          child: controller.obx(
            (state) => Obx(() => _list()),
            onError: ListWidget.message,
            onEmpty: ListWidget.blank(),
          ),
        ),
      ),
    );
    return Column(children: children);
  }
}

class _Banner extends StatelessWidget {
  final num memberCount;
  final num vipCount;

  const _Banner({
    Key key,
    this.memberCount,
    this.vipCount,
  }) : super(key: key);

  num get _normal {
    final x = memberCount ?? 0;
    final y = vipCount ?? 0;
    return max(0, x - y);
  }

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.addIf(true, SizedBox(width: 12));
    children.addIf(true, _memberIcon());
    children.addIf(true, SizedBox(width: 4));
    children.addIf(true, _memberCount());
    children.addIf(true, SizedBox(width: 4));
    children.addIf(true, Vip());
    children.addIf(true, SizedBox(width: 4));
    children.addIf(true, _memberCountDetail());
    return Row(children: children);
  }

  Widget _memberCountDetail() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 16,
          color: AppColors.Gray33,
        ),
        children: [
          TextSpan(
            text: '會員',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Gray66,
            ),
          ),
          TextSpan(
            // text: '268',
            text: vipCount?.decimalStyle ?? '0',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: ')',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Gray66,
            ),
          ),
        ],
      ),
    );
  }

  Widget _memberCount() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 16,
          color: AppColors.Gray33,
        ),
        children: [
          TextSpan(
            text: '會員人數',
          ),
          TextSpan(
            text: ' ',
            style: TextStyle(
              color: AppColors.Gray66,
            ),
          ),
          TextSpan(
            // text: '1,268',
            text: memberCount?.decimalStyle ?? '0',
            style: TextStyle(
              color: AppColors.Primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: '  ',
            style: TextStyle(
              color: AppColors.Primary,
            ),
          ),
          TextSpan(
            text: '(一般會員',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Gray66,
            ),
          ),
          TextSpan(
            // text: '1,000',
            text: _normal.decimalStyle,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: ' /',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.Gray66,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _memberIcon() {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.Primary,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.group,
        color: Colors.white,
        size: 16,
      ),
    );
  }
}
