import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/square_toggle.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/order_filter_picker_controller.dart';

class OrderFilterPickerView extends GetView<OrderFilterPickerController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderFilterPickerController>(
      init: OrderFilterPickerController(
        tableProvider: Get.find(),
      ),
      builder: (controller) {
        return StandardPage(
          backgroundColor: controller.prefProvider.themeColor,
          titleText: controller.titleText,
          child: controller.obx((state) {
            return BottomWidgetPage(
              child: Obx(() => _child()),
              bottom: _bottom(),
            );
          }),
        );
      },
    );
  }

  Widget _bottom() {
    return Container(
      height: kBottomButtonPadding,
      decoration: const BoxDecoration(
        gradient: const LinearGradient(
          begin: const Alignment(0.0, -1.0),
          end: const Alignment(0.0, -0.45),
          colors: const [
            const Color(0x00FFFFFF),
            Colors.white,
          ],
          stops: const [0.0, 1.0],
        ),
      ),
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: kPadding,
      ),
      child: YesNoButton(
        onLeftPressed: () => Get.back(),
        leftButtonText: '取消',
        rightButtonText: '開始搜尋',
        onRightPressed: _submit,
      ),
    );
  }

  Widget _child() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: 32));
    children.addIf(
      true,
      SettingsWidget.input(
        labelText: '訂單編號',
        hintText: '請輸入訂單編號末3碼',
        onChanged: (value) {
          controller.draft.keyword = value;
        },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        labelText: '電話',
        hintText: '請輸入電話末3碼',
        onChanged: (value) {
          controller.draft.mobilePhone = value;
        },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        labelText: '會員暱稱',
        hintText: '請輸入會員暱稱',
        onChanged: (value) {
          controller.draft.name = value;
        },
      ),
    );

    children.addIf(true, SettingsWidget.space());
    children.addIf(true, SizedBox(height: 12));
    children.addIf(controller.prefProvider.storeType.isDinner, _table());

    children.addIf(true, SizedBox(height: kBottomButtonPadding));
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  Widget _table() {
    final children = <Widget>[];

    children.addIf(
      controller.data.isNotEmpty,
      Text(
        '區域',
        style: TextStyle(
          fontSize: 23,
          color: AppColors.Gray22,
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        vertical: 12,
      ),
    );

    children.addIf(
      controller.data.isNotEmpty,
      _area1().paddingOnly(
        bottom: 12,
      ),
    );

    children.addIf(
      controller.currentTableList.isNotEmpty,
      SettingsWidget.divider().paddingOnly(
        bottom: 12,
      ),
    );

    children.addIf(
      controller.currentTableList.isNotEmpty,
      Text(
        '桌號',
        style: TextStyle(
          fontSize: 23,
          color: AppColors.Gray22,
        ),
        textAlign: TextAlign.center,
      ).paddingOnly(
        bottom: 12,
      ),
    );

    children.addIf(
      controller.currentTableList.isNotEmpty,
      _area2().paddingOnly(
        bottom: 12,
      ),
    );

    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }

  Widget _area1() {
    final children = controller.data.map((element) {
      return SquareToggle<num>(
        backgroundColor: controller.prefProvider.themeColor,
        value: element.id,
        text: element.name,
        checked: controller.draft.table1Id == element.id,
        onPressed: (value) {
          controller.draft.table1Id = value;
          controller.trySelectTheFirstTable();
          controller.refreshDraft();
        },
      );
    });
    return GridView.count(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      crossAxisCount: 4,
      childAspectRatio: 3.0 / 1.75,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      children: children.toList(),
    ).paddingSymmetric(horizontal: kPadding);
  }

  Widget _area2() {
    final children = controller.currentTableList.map((e) {
      return SquareToggle<num>(
        backgroundColor: controller.prefProvider.themeColor,
        value: e.id,
        text: e.name,
        checked: controller.draft.table2Id == e.id,
        onPressed: (value) {
          controller.draft.table2Id = value;
          controller.refreshDraft();
        },
      );
    });
    return GridView.count(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      crossAxisCount: 4,
      childAspectRatio: 3.0 / 1.75,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      children: children.toList(),
    ).paddingSymmetric(horizontal: kPadding);
  }

  void _submit() {
    if (controller.draft.table1Id == 0) {
      controller.draft.table1Id = null;
    }
    if (controller.draft.table2Id == 0) {
      controller.draft.table2Id = null;
    }
    final json = controller.draft.toJson();
    json.removeWhere((key, value) {
      if (key == null || value == null) {
        return true;
      }
      if (value is String && value.isEmpty) {
        return true;
      }
      return false;
    });
    Get.back(result: jsonEncode(json));
  }
}
