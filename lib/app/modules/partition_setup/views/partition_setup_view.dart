import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/composite_edit_item.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/theme/app_colors.dart';

import '../controllers/partition_setup_controller.dart';

// 編輯區域
class PartitionSetupView extends GetView<PartitionSetupController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '餐飲桌號設定',
      child: controller.obx((state) {
        return BottomWidgetPage.save(
          child: _main(),
          onPressed: _submit,
        );
      }),
    );
  }

  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(
      true,
      Text(
        '區域設定',
        style: TextStyle(
          fontSize: 16,
          color: AppColors.Primary,
        ),
      ),
    );
    children.addIf(true, SizedBox(height: 12));
    children.addIf(
      true,
      Expanded(child: Obx(() => _list())),
    );
    return Column(children: children);
  }

  Widget _list() {
    final list = controller.data;
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final children = List.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.ParentButton,
            CompositeRightButtonMode.Remove,
            mainButtonText: element.name,
            onMainButtonPressed: () {
              Get.toNamed(
                Routes.TABLE_SETUP,
                parameters: <String, String>{
                  Keys.Data: element.toRawJson(),
                },
              );
            },
            onRightButtonPressed: () async {
              // 刪除區域
              controller.deleting(element.id);
              return true;
            },
          ),
          key: Key('$index'),
        );
      }
      //Add new partition.
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.OneTextField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '選項名稱',
          mainTextFieldChanged: (value) {
            controller.req.name = value;
          },
          // 新增
          onRightButtonPressed: () {
            final name = controller.req.name;
            if (name == null || name.isEmpty) {
              return DialogGeneral.alert('請先輸入選項名稱').dialog();
            }
            //呼叫新增 Table API.
            return controller.addNewTable();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
      onReorder: (srcIndex, destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sort(srcIndex, destIndex);
      },
    );
  }
}
