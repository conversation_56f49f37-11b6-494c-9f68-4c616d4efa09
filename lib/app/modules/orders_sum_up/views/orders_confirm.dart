import 'package:flutter/material.dart';
import 'package:muyipork/app/components/label_value_text.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/extension.dart';

class OrdersConfirm extends StatelessWidget {
  final OrdersPostReq data;
  final PrefProvider prefProvider;

  const OrdersConfirm({
    this.data,
    this.prefProvider,
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Text(
      '按下確認後，將成立此筆消費',
      style: TextStyle(
        fontSize: 16,
        color: AppColors.Gray66,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: 16);
    yield _labelValueText(
      '商品小計',
      (data.subtotal ?? 0).currencyStyle,
    );
    if (serviceFeeVisible == true) {
      yield _labelValueText(
        '服務費',
        (data.fee ?? 0).currencyStyle,
      );
    }
    if (shippingFeeVisible == true) {
      yield _labelValueText(
        '運費',
        (data.shippingFee ?? 0).currencyStyle,
      );
    }
    if (paymentFeeVisible == true) {
      yield _labelValueText(
        '金流手續費',
        (data.paymentFee ?? 0).currencyStyle,
      );
    }
    yield _labelValueText(
      '現場減價',
      (data.discount ?? 0).currencyStyle,
    );
    // TODO: refactor me.
    // 優惠券金額
    if (data.memberCouponExtraPrice != null &&
        data.memberCouponExtraPrice.abs() > 0) {
      yield _labelValueText(
        '優惠券金額',
        (data.memberCouponExtraPrice ?? 0).currencyStyle,
      );
    }
    // TODO: refactor me.
    // 優惠券折抵
    if (data.memberCouponDiscount != null &&
        data.memberCouponDiscount.abs() > 0) {
      yield _labelValueText(
        '優惠券折抵',
        (0 - (data.memberCouponDiscount ?? 0).abs()).currencyStyle,
      );
    }
    yield _labelValueText(
      '額外費用',
      (data.additionalCharges ?? 0).currencyStyle,
    );
    if (pointDiscountVisible == true) {
      yield _labelValueText(
        '使用積點',
        (0 - (data.redeemMemberPoints ?? 0)).currencyStyle,
      );
    }
    yield _labelValueText(
      '商品總價',
      (data.total ?? 0).currencyStyle,
    );
    yield _labelValueText(
      '實收',
      (data.paid ?? 0).currencyStyle,
    );
    yield LabelValueText(
      left: TextArgs.left(
        '現金找零',
        fontSize: 16,
      ),
      right: TextArgs.right(
        (data.change ?? 0).currencyStyle,
        fontSize: 16,
        color: const Color(0xffe00707),
      ),
    );
  }

  Widget _labelValueText(
    String label,
    String value, {
    Color labelColor = Colors.black,
    Color valueColor = Colors.black,
  }) {
    return LabelValueText(
      left: TextArgs.left(
        label,
        color: labelColor,
        fontSize: 16,
      ),
      right: TextArgs.right(
        value,
        color: valueColor,
        fontSize: 16,
      ),
    );
  }

  // 顯示運費，條件:
  // 1. 零售宅配
  // 2. 設定啟用運費
  bool get shippingFeeVisible {
    return data.orderType.isRetailDelivery &&
        prefProvider.shippingDelivery.enabled;
  }

  // 顯示金流手續費，條件:
  // 1. 零售
  // 2. 線上
  bool get paymentFeeVisible {
    return data.orderType.isRetail && data.orderSource.isLine;
  }

  // 顯示服務費，條件:
  // 1. 餐飲內用
  // 2. 設定啟用任何服務費
  bool get serviceFeeVisible {
    final type = prefProvider.settingData.settingOrderFee.serviceFeeType;
    return data.orderType.isDinnerHere &&
        [ServiceFeeType.Origin, ServiceFeeType.Discount].contains(type);
  }

  // 顯示積點折抵，條件: 有會員
  bool get pointDiscountVisible {
    return data.memberId is num && data.memberId > 0;
  }
}
