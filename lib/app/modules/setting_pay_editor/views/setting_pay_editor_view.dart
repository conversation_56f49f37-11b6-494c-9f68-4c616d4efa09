import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/paymethod_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/setting_pay.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/theme/app_colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_pay_editor_controller.dart';

class SettingPayEditorView extends GetView<SettingPayEditorController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions(),
      titleText: '支付方式設定',
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            child: _main(),
            onPressed: _submit,
          );
        },
        onError: ListWidget.message,
      ),
    );
  }

  List<Widget> _actions() {
    final children = <Widget>[];
    children.add(
      TextButton(
        onPressed: () {
          Get.toNamed(Routes.SETTING_PAY_ADD);
        },
        child: Text(
          '新增',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
    return children;
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(SizedBox(height: 12));
    children.add(
      Text(
        '請點選以下項目，即可切換選取類別',
        style: TextStyle(
          fontSize: 16,
          color: AppColors.Primary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    children.add(SizedBox(height: 12));
    children.add(
      Expanded(
        child: ColoredBox(
          color: Colors.white,
          child: Obx(() => _list()),
        ),
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _sliverList(List<SettingPay> list) {
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (content, index) {
          final element = list.elementAt(index);
          return _item(element).paddingSymmetric(
            horizontal: kPadding,
            vertical: 4,
          );
        },
        childCount: list.length,
      ),
    );
  }

  Widget _switchOffList() {
    final list = controller.draft
        .where((element) => element.status.switcher.isOff)
        .toList(growable: false);
    return _sliverList(list);
  }

  Widget _switchOnList() {
    final list = controller.draft
        .where((element) => element.status.switcher.isOn)
        .toList(growable: false);
    return _sliverList(list);
  }

  Widget _list() {
    final children = <Widget>[];
    children.add(
      SizedBox(height: 28).sliverBox,
    );
    children.add(
      SliverPadding(
        padding: EdgeInsets.symmetric(
          horizontal: kPadding,
        ),
        sliver: Text(
          '已選取項目',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.Primary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.left,
        ).sliverBox,
      ),
    );
    children.add(
      SizedBox(height: 6).sliverBox,
    );
    children.add(
      SliverPadding(
        padding: EdgeInsets.symmetric(
          horizontal: kPadding,
        ),
        sliver: Text(
          '已選取的項目會出現在結帳頁面的支付方式選項中\n點選icon可變更「已選取」或「未選取」狀態。',
          style: TextStyle(
            fontSize: 14,
            color: const Color(0xff8e8e8e),
          ),
          textAlign: TextAlign.left,
        ).sliverBox,
      ),
    );
    children.add(
      SizedBox(height: 12).sliverBox,
    );
    children.add(_switchOnList());
    children.add(
      SizedBox(height: 28).sliverBox,
    );
    children.add(
      SliverPadding(
        padding: EdgeInsets.symmetric(
          horizontal: kPadding,
        ),
        sliver: Text(
          '未選取項目',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.Primary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.left,
        ).sliverBox,
      ),
    );
    children.add(
      SizedBox(height: 12).sliverBox,
    );
    children.add(_switchOffList());
    children.add(
      SizedBox(height: kBottomButtonPadding).sliverBox,
    );
    return CustomScrollView(
      physics: AlwaysScrollableScrollPhysics(),
      slivers: children,
    );
  }

  Widget _item(SettingPay data) {
    final children = <Widget>[];
    children.addIf(
      true,
      GestureDetector(
        onTap: () {
          data.status = (data.status + 1) % 2;
          controller.draft.refresh();
        },
        child: PaymethodItem(
          title: data.name,
          image: data.payMethodId.appPayMethod.icon,
          showTitle: data.isCustom ?? false,
        ),
      ),
    );
    children.addIf(
      data.isCustom ?? false,
      Padding(
        padding: const EdgeInsets.only(
          left: 12,
        ),
        child: _editor(data),
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _editor(final SettingPay data) {
    final children = <Widget>[];
    children.add(
      _box(
        child: SizedBox.fromSize(
          size: Size.square(68),
          child: IconButton(
            icon: Icon(
              Icons.delete_forever,
              color: AppColors.Primary,
              size: 30,
            ),
            onPressed: () {
              final index = controller.draft
                  .indexWhere((element) => element.id == data.id);
              if (index >= 0) {
                controller.draft.removeAt(index);
                controller.draft.refresh();
              }
            },
          ),
        ),
      ),
    );
    children.add(SizedBox(width: 12));
    children.add(
      _box(
        child: SizedBox.fromSize(
          size: Size.square(68),
          child: IconButton(
            icon: Icon(
              Icons.edit,
              color: AppColors.Primary,
              size: 30,
            ),
            onPressed: () {
              Get.toNamed(
                Routes.SETTING_PAY_ADD,
                parameters: {
                  'id': '${data.id}',
                },
              );
            },
          ),
        ),
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  void _submit() {
    FutureProgress<bool>(
      future: controller.submit(),
    ).dialog<bool>().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }
}

class _box extends StatelessWidget {
  final Widget child;

  const _box({
    Key key,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.0),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0x1a000000),
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: child ?? SizedBox(),
    );
  }
}
