import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:muyipork/theme/app_colors.dart';

void main() {
  group('AppColors Tests', () {
    test('主要颜色应该正确定义', () {
      expect(AppColors.primary, const Color(0xFFF89328));
      expect(AppColors.primaryWeight, const Color(0xFFE66F53));
      expect(AppColors.secondary, const Color(0xFFF7AB00));
      expect(AppColors.accent, const Color(0xFFED4C00));
    });

    test('功能颜色应该正确定义', () {
      expect(AppColors.error, const Color(0xFFE02020));
      expect(AppColors.destructive, AppColors.error);
      expect(AppColors.must, const Color(0xffe00707));
      expect(AppColors.vip, const Color(0xfffa5700));
      expect(AppColors.retail, const Color(0xFFE0A471));
      expect(AppColors.line, const Color(0xFF00c330));
      expect(AppColors.tab, const Color(0xff3e4b5a));
      expect(AppColors.shadow, const Color(0x29000000));
    });

    test('灰色系列应该正确定义', () {
      expect(AppColors.gray22, const Color(0xff222222));
      expect(AppColors.gray33, const Color(0xff333333));
      expect(AppColors.gray4D, const Color(0xff4d4d4d));
      expect(AppColors.gray58, const Color(0xff585858));
      expect(AppColors.gray66, const Color(0xff666666));
      expect(AppColors.gray70, const Color(0xFF707070));
      expect(AppColors.grayB9, const Color(0xffb9b9b9));
      expect(AppColors.grayBF, const Color(0xffbfbfbf));
      expect(AppColors.grayDD, const Color(0xffdddddd));
      expect(AppColors.grayF7, const Color(0xFFF7F7F7));
    });

    test('兼容性别名应该正确工作', () {
      expect(AppColors.Primary, AppColors.primary);
      expect(AppColors.PrimaryWeight, AppColors.primaryWeight);
      expect(AppColors.Secondary, AppColors.secondary);
      expect(AppColors.Accent, AppColors.accent);
      expect(AppColors.Error, AppColors.error);
      expect(AppColors.Retail, AppColors.retail);
      expect(AppColors.Gray66, AppColors.gray66);
      expect(AppColors.GrayDD, AppColors.grayDD);
    });
  });
}
